const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const { spawn } = require('child_process');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const app = express();
const PORT = process.env.TOKEN_API_PORT || 9043;
const AUTH_PASSWORD = process.env.AUTH_PASSWORD;
const TOKENS_FILE = path.join(__dirname, '../tokens.json');

// Load configuration
const CONFIG_FILE = path.join(__dirname, 'config.json');
let config;
try {
  config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));

  // Auto-detect Python command based on platform
  if (config.automationScript.command === 'python3' && process.platform === 'win32') {
    config.automationScript.command = 'python';
    console.log('[CONFIG] Auto-detected Windows: using "python" instead of "python3"');
  } else if (config.automationScript.command === 'python' && process.platform !== 'win32') {
    config.automationScript.command = 'python3';
    console.log('[CONFIG] Auto-detected Unix-like system: using "python3" instead of "python"');
  }
} catch (error) {
  console.error('Error loading config file:', error);
  process.exit(1);
}

// Initialize SQLite database
const DB_PATH = path.join(__dirname, config.database.path);
const db = new sqlite3.Database(DB_PATH);

// Initialize database tables
db.serialize(() => {
  db.run(`CREATE TABLE IF NOT EXISTS tokens (
    id TEXT PRIMARY KEY,
    access_token TEXT NOT NULL,
    tenant_url TEXT NOT NULL,
    description TEXT,
    email_note TEXT,
    user_agent TEXT,
    session_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    used BOOLEAN DEFAULT 0,
    created_timestamp INTEGER
  )`);
});

// Middleware
app.use(cors());
app.use(express.json());

// Authentication middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token || token !== AUTH_PASSWORD) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Invalid authentication token'
    });
  }

  next();
}

// Helper function to read tokens from file
function readTokens() {
  try {
    const data = fs.readFileSync(TOKENS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading tokens file:', error);
    return [];
  }
}

// Helper function to write tokens to file
function writeTokens(tokens) {
  try {
    fs.writeFileSync(TOKENS_FILE, JSON.stringify(tokens, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('Error writing tokens file:', error);
    return false;
  }
}

// Helper function to find and mark an unused token from SQLite
function getAndMarkUnusedToken() {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT * FROM tokens WHERE used = 0 ORDER BY created_at ASC LIMIT 1`,
      (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        // Mark token as used
        db.run(
          `UPDATE tokens SET used = 1 WHERE id = ?`,
          [row.id],
          (updateErr) => {
            if (updateErr) {
              reject(updateErr);
              return;
            }

            resolve({
              id: row.id,
              accessToken: row.access_token,
              tenantURL: row.tenant_url,
              description: row.description || 'Token from database',
              createdAt: row.created_at
            });
          }
        );
      }
    );
  });
}

// Helper function to get valid token count
function getValidTokenCount() {
  return new Promise((resolve, reject) => {
    const validityMs = config.tokenValidityDays * 24 * 60 * 60 * 1000;
    const bufferMs = config.validityBufferHours * 60 * 60 * 1000;
    const cutoffTime = new Date(Date.now() - validityMs + bufferMs);

    db.get(
      `SELECT COUNT(*) as count FROM tokens
       WHERE used = 0 AND created_at > datetime(?)`,
      [cutoffTime.toISOString()],
      (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(row.count);
      }
    );
  });
}

// Helper function to save token to SQLite
function saveTokenToDatabase(tokenData) {
  return new Promise((resolve, reject) => {
    const {
      id,
      access_token,
      tenant_url,
      description,
      email_note,
      user_agent,
      session_id,
      created_timestamp
    } = tokenData;

    const createdAt = created_timestamp ? new Date(created_timestamp).toISOString() : new Date().toISOString();

    db.run(
      `INSERT INTO tokens (id, access_token, tenant_url, description, email_note, user_agent, session_id, created_at, created_timestamp)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, access_token, tenant_url, description, email_note, user_agent, session_id, createdAt, created_timestamp],
      function (err) {
        if (err) {
          reject(err);
          return;
        }
        resolve({ id: this.lastID, tokenId: id });
      }
    );
  });
}

// Helper function to log summary
function logSummary(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ${message}\n`;

  const logDir = path.dirname(config.logging.summaryLogPath);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  fs.appendFileSync(config.logging.summaryLogPath, logMessage);
  console.log(`[SUMMARY] ${message}`);
}

// Function to trigger automation script (sequential execution)
async function triggerAutomationScript(count = 1, reason = 'Scheduled check') {
  logSummary(`${reason}: Starting ${count} automation script(s) sequentially`);

  for (let i = 0; i < count; i++) {
    try {
      await runSingleScript(i + 1, count, reason);

      // Wait 3 seconds between scripts to avoid conflicts
      if (i < count - 1) {
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    } catch (error) {
      console.error(`[AUTOMATION] Script ${i + 1}/${count} failed:`, error.message);
      logSummary(`Script ${i + 1}/${count} failed: ${error.message}`);
      // Continue with next script even if current one fails
    }
  }

  logSummary(`${reason}: Completed ${count} automation script(s)`);
}

// Function to run a single automation script with timeout
function runSingleScript(current, total, reason) {
  return new Promise((resolve, reject) => {
    const scriptPath = path.join(__dirname, config.automationScript.workingDirectory);
    const timeout = 5 * 60 * 1000; // 5 minutes timeout

    console.log(`[AUTOMATION] Starting script ${current}/${total}`);

    const child = spawn(config.automationScript.command, config.automationScript.args, {
      cwd: scriptPath,
      detached: false,  // Keep attached for monitoring
      stdio: ['ignore', 'pipe', 'pipe'],  // Capture stdout and stderr for monitoring
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',  // Force UTF-8 encoding for Python
        PYTHONUNBUFFERED: '1'       // Disable Python output buffering
      }
    });

    let stdout = '';
    let stderr = '';
    const startTime = Date.now();

    // Capture output for analysis
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    // Set timeout
    const timeoutId = setTimeout(() => {
      console.log(`[AUTOMATION] Script ${current}/${total} (PID: ${child.pid}) timed out after 5 minutes, killing...`);
      child.kill('SIGTERM');

      // Force kill after 5 seconds if still running
      setTimeout(() => {
        if (!child.killed) {
          child.kill('SIGKILL');
        }
      }, 5000);
    }, timeout);

    // Handle process completion
    child.on('close', (code, signal) => {
      clearTimeout(timeoutId);
      const duration = Math.round((Date.now() - startTime) / 1000);

      if (signal) {
        console.log(`[AUTOMATION] Script ${current}/${total} (PID: ${child.pid}) killed by signal ${signal} after ${duration}s`);
        logSummary(`Script ${current}/${total} killed by ${signal} after ${duration}s`);
        reject(new Error(`Process killed by signal ${signal}`));
      } else if (code === 0) {
        console.log(`[AUTOMATION] Script ${current}/${total} (PID: ${child.pid}) completed successfully in ${duration}s`);
        logSummary(`Script ${current}/${total} completed successfully in ${duration}s`);

        // Analyze output for success indicators
        analyzeScriptOutput(stdout, stderr, current, total);
        resolve();
      } else {
        console.log(`[AUTOMATION] Script ${current}/${total} (PID: ${child.pid}) failed with exit code ${code} after ${duration}s`);
        logSummary(`Script ${current}/${total} failed with exit code ${code} after ${duration}s`);

        // Log error details for debugging
        if (stderr.trim()) {
          console.error(`[AUTOMATION] Script ${current}/${total} stderr:`, stderr.slice(-300));
        }

        reject(new Error(`Process exited with code ${code}`));
      }
    });

    child.on('error', (error) => {
      clearTimeout(timeoutId);
      console.error(`[AUTOMATION] Failed to start script ${current}/${total}:`, error.message);
      logSummary(`Failed to start script ${current}/${total}: ${error.message}`);
      reject(error);
    });

    console.log(`[AUTOMATION] Script ${current}/${total} started with PID: ${child.pid}`);
  });
}

// Analyze script output for success/failure indicators
function analyzeScriptOutput(stdout, stderr, current, total) {
  const successPatterns = [
    /token.*saved.*successfully/i,
    /✅.*token/i,
    /successfully.*generated.*token/i,
    /token.*api.*success/i
  ];

  const errorPatterns = [
    /error.*occurred/i,
    /failed.*to/i,
    /exception/i,
    /❌/i
  ];

  let foundSuccess = false;
  let foundError = false;

  // Check for success indicators
  for (const pattern of successPatterns) {
    if (pattern.test(stdout)) {
      foundSuccess = true;
      break;
    }
  }

  // Check for error indicators
  for (const pattern of errorPatterns) {
    if (pattern.test(stdout) || pattern.test(stderr)) {
      foundError = true;
      break;
    }
  }

  if (foundSuccess) {
    console.log(`[AUTOMATION] Script ${current}/${total} output indicates token generation success`);
    logSummary(`Script ${current}/${total} output indicates success`);
  } else if (foundError) {
    console.log(`[AUTOMATION] Script ${current}/${total} output indicates errors occurred`);
    logSummary(`Script ${current}/${total} output indicates errors`);
  }

  // Log sample output for debugging (last 200 chars)
  if (stdout.trim()) {
    console.log(`[AUTOMATION] Script ${current}/${total} stdout sample:`, stdout.slice(-200).trim());
  }
}

// Scheduled task to check token count
async function checkTokenCount() {
  try {
    const validCount = await getValidTokenCount();
    const minRequired = config.minValidTokens;

    logSummary(`Scheduled check: ${validCount} valid tokens found (minimum required: ${minRequired})`);

    if (validCount < minRequired) {
      const scriptsToRun = minRequired - validCount;
      // Run automation scripts asynchronously without blocking the check
      triggerAutomationScript(scriptsToRun, 'Low token count detected').catch(error => {
        console.error('Error in automation script execution:', error);
        logSummary(`Automation script execution error: ${error.message}`);
      });
    }

  } catch (error) {
    console.error('Error in scheduled token check:', error);
    logSummary(`Scheduled check failed: ${error.message}`);
  }
}

// Start scheduled task
const checkInterval = config.checkIntervalHours * 60 * 60 * 1000; // Convert hours to milliseconds
setInterval(checkTokenCount, checkInterval);

// Run initial check after 30 seconds
setTimeout(checkTokenCount, 30000);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Token API is running',
    timestamp: new Date().toISOString()
  });
});

// Main API endpoint - Get one unused token
app.get('/api/tokens', authenticateToken, async (req, res) => {
  try {
    const token = await getAndMarkUnusedToken();

    if (!token) {
      return res.status(404).json({
        success: false,
        error: 'No available tokens',
        message: 'All tokens have been used or no tokens found',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      token: token,
      timestamp: new Date().toISOString()
    });

    console.log(`Token ${token.id} has been returned and marked as used`);

  } catch (error) {
    console.error('Error processing token request:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to process token request',
      timestamp: new Date().toISOString()
    });
  }
});

// Get token statistics (for debugging)
app.get('/api/tokens/stats', authenticateToken, async (req, res) => {
  try {
    const validCount = await getValidTokenCount();

    // Get total counts from database
    db.get(
      `SELECT
        COUNT(*) as total,
        SUM(CASE WHEN used = 1 THEN 1 ELSE 0 END) as used,
        SUM(CASE WHEN used = 0 THEN 1 ELSE 0 END) as unused
       FROM tokens`,
      (err, row) => {
        if (err) {
          console.error('Error getting token stats:', err);
          return res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: 'Failed to get token statistics'
          });
        }

        res.json({
          success: true,
          stats: {
            total: row.total,
            used: row.used,
            unused: row.unused,
            valid: validCount
          },
          timestamp: new Date().toISOString()
        });
      }
    );
  } catch (error) {
    console.error('Error getting token stats:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to get token statistics'
    });
  }
});

// API 1: Save token from automation script
app.post('/api/tokens/save', authenticateToken, async (req, res) => {
  try {
    const tokenData = req.body;

    // Validate required fields
    if (!tokenData.access_token || !tokenData.tenant_url) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        message: 'access_token and tenant_url are required'
      });
    }

    // Generate ID if not provided
    if (!tokenData.id) {
      tokenData.id = require('crypto').randomUUID();
    }

    const result = await saveTokenToDatabase(tokenData);

    res.json({
      success: true,
      message: 'Token saved successfully',
      tokenId: result.tokenId,
      timestamp: new Date().toISOString()
    });

    console.log(`Token ${result.tokenId} saved to database`);

  } catch (error) {
    console.error('Error saving token:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to save token'
    });
  }
});

// API 2: Get valid token count
app.get('/api/tokens/valid-count', authenticateToken, async (req, res) => {
  try {
    const validCount = await getValidTokenCount();

    res.json({
      success: true,
      validCount: validCount,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting valid token count:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to get valid token count'
    });
  }
});

// API 4: Trigger automation script manually
app.post('/api/tokens/trigger-automation', authenticateToken, async (req, res) => {
  try {
    const scriptCount = req.body.count || 1;

    res.json({
      success: true,
      message: `Triggering automation script ${scriptCount} time(s)`,
      timestamp: new Date().toISOString()
    });

    // Trigger automation script asynchronously
    triggerAutomationScript(scriptCount, 'Manual trigger').catch(error => {
      console.error('Error in manual automation trigger:', error);
      logSummary(`Manual automation trigger error: ${error.message}`);
    });

  } catch (error) {
    console.error('Error triggering automation:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to trigger automation'
    });
  }
});



// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not Found',
    message: 'Endpoint not found'
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  });
});

// Start server
app.listen(PORT, async () => {
  console.log(`Token API server is running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`API endpoints:`);
  console.log(`  GET  /api/tokens - Get unused token`);
  console.log(`  POST /api/tokens/save - Save new token`);
  console.log(`  GET  /api/tokens/valid-count - Get valid token count`);
  console.log(`  GET  /api/tokens/stats - Get token statistics`);
  console.log(`  POST /api/tokens/trigger-automation - Trigger automation script`);
  console.log(`Authentication: Bearer ${AUTH_PASSWORD ? '[CONFIGURED]' : '[NOT CONFIGURED]'}`);
  console.log(`Database: ${DB_PATH}`);
  console.log(`Config: Min valid tokens: ${config.minValidTokens}, Check interval: ${config.checkIntervalHours}h`);

  try {
    const validCount = await getValidTokenCount();
    console.log(`Current valid tokens: ${validCount}`);
    logSummary(`Server started - ${validCount} valid tokens available`);
  } catch (error) {
    console.error('Error checking initial token count:', error);
  }
});

module.exports = app;
