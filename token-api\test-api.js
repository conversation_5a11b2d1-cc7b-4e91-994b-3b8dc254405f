#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9043';
const AUTH_TOKEN = 'your_secret_password_here_change_this'; // From .env file

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};

async function testAPI() {
  console.log('🧪 Testing Token API endpoints...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check:', healthResponse.data.message);

    // Test 2: Get valid token count
    console.log('\n2. Testing valid token count...');
    const validCountResponse = await axios.get(`${BASE_URL}/api/tokens/valid-count`, { headers });
    console.log('✅ Valid token count:', validCountResponse.data.validCount);

    // Test 3: Get token statistics
    console.log('\n3. Testing token statistics...');
    const statsResponse = await axios.get(`${BASE_URL}/api/tokens/stats`, { headers });
    console.log('✅ Token stats:', statsResponse.data.stats);

    // Test 4: Save a new token
    console.log('\n4. Testing save new token...');
    const newToken = {
      id: `test-${Date.now()}`,
      access_token: `test_token_${Date.now()}`,
      tenant_url: 'https://test.api.augmentcode.com/',
      description: 'Test token from API test',
      email_note: '<EMAIL>',
      user_agent: 'test-api-client',
      session_id: `test_session_${Date.now()}`,
      created_timestamp: Date.now()
    };

    const saveResponse = await axios.post(`${BASE_URL}/api/tokens/save`, newToken, { headers });
    console.log('✅ Token saved:', saveResponse.data.message);

    // Test 5: Get an unused token
    console.log('\n5. Testing get unused token...');
    const tokenResponse = await axios.get(`${BASE_URL}/api/tokens`, { headers });
    console.log('✅ Got token:', {
      id: tokenResponse.data.token.id,
      tenantURL: tokenResponse.data.token.tenantURL
    });

    // Test 6: Trigger automation (manual)
    console.log('\n6. Testing trigger automation...');
    const triggerResponse = await axios.post(`${BASE_URL}/api/tokens/trigger-automation`, 
      { count: 1 }, 
      { headers }
    );
    console.log('✅ Automation triggered:', triggerResponse.data.message);

    console.log('\n🎉 All API tests passed!');

  } catch (error) {
    console.error('❌ API test failed:', error.response?.data || error.message);
  }
}

// Run tests
testAPI();
