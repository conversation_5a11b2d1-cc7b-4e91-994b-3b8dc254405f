#!/usr/bin/env python3
"""
Example integration script showing how automation scripts should call the Token API
to save newly generated tokens.

This script demonstrates the integration pattern that should be used in:
- drissionpage-automation-firefox/run_firefox_fixed.py
- drissionpage-automation-enchanced/drissionpage_automation.py
- real-browser-automation/real-browser-automation.js
"""

import requests
import json
import time
import uuid
import os
from typing import Dict, Optional

class TokenAPIClient:
    def __init__(self, api_url: str = "http://localhost:9043", auth_password: str = None):
        self.api_url = api_url
        self.auth_password = auth_password or os.getenv('AUTH_PASSWORD', 'your_secret_password_here_change_this')
        self.headers = {
            "Authorization": f"Bearer {self.auth_password}",
            "Content-Type": "application/json"
        }
    
    def save_token(self, access_token: str, tenant_url: str, **kwargs) -> Dict:
        """
        Save a new token to the API database.
        
        Args:
            access_token: The authentication token
            tenant_url: The tenant URL
            **kwargs: Additional optional fields (description, email_note, user_agent, etc.)
        
        Returns:
            API response dictionary
        """
        # Generate ID if not provided
        token_id = kwargs.get('id', str(uuid.uuid4()))
        
        data = {
            "id": token_id,
            "access_token": access_token,
            "tenant_url": tenant_url,
            "created_timestamp": int(time.time() * 1000),  # Current timestamp in milliseconds
            **kwargs  # Include any additional fields
        }
        
        try:
            response = requests.post(f"{self.api_url}/api/tokens/save", headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error saving token to API: {e}")
            return {"success": False, "error": str(e)}
    
    def get_valid_count(self) -> int:
        """Get the current count of valid tokens."""
        try:
            response = requests.get(f"{self.api_url}/api/tokens/valid-count", headers=self.headers)
            response.raise_for_status()
            return response.json().get('validCount', 0)
        except requests.exceptions.RequestException as e:
            print(f"Error getting valid count: {e}")
            return 0
    
    def get_unused_token(self) -> Optional[Dict]:
        """Get an unused token from the API."""
        try:
            response = requests.get(f"{self.api_url}/api/tokens", headers=self.headers)
            response.raise_for_status()
            return response.json().get('token')
        except requests.exceptions.RequestException as e:
            print(f"Error getting unused token: {e}")
            return None

# Example usage for DrissionPage automation
def example_drissionpage_integration():
    """
    Example showing how to integrate with DrissionPage automation scripts.
    This pattern should be used in run_firefox_fixed.py
    """
    api_client = TokenAPIClient()
    
    # Simulate successful token generation from automation
    # In real implementation, this would come from your automation process
    access_token = "example_token_" + str(int(time.time()))
    tenant_url = "https://d1.api.augmentcode.com/"
    email_used = "<EMAIL>"
    
    # Save the token to the API
    result = api_client.save_token(
        access_token=access_token,
        tenant_url=tenant_url,
        description="Firefox token from fixed automation",
        email_note=email_used,
        user_agent="firefox-fixed-automation",
        session_id=f"firefox_fixed_{int(time.time())}"
    )
    
    if result.get('success'):
        print(f"✅ Token saved successfully: {result.get('tokenId')}")
        print(f"📊 Current valid tokens: {api_client.get_valid_count()}")
    else:
        print(f"❌ Failed to save token: {result.get('message')}")

# Example usage for Real Browser automation
def example_real_browser_integration():
    """
    Example showing how to integrate with Real Browser automation scripts.
    This pattern should be used in real-browser-automation.js (converted to Python calls)
    """
    api_client = TokenAPIClient()
    
    # Simulate successful token generation
    access_token = "real_browser_token_" + str(int(time.time()))
    tenant_url = "https://d2.api.augmentcode.com/"
    
    result = api_client.save_token(
        access_token=access_token,
        tenant_url=tenant_url,
        description="Real Browser token from Augment API via email verification",
        user_agent="real-browser-email-verification",
        session_id=f"real_browser_session_{int(time.time())}"
    )
    
    print(f"Real Browser Integration Result: {result}")

# Example of how to modify existing automation scripts
def integration_template():
    """
    Template showing the minimal changes needed in existing automation scripts.
    """
    api_client = TokenAPIClient()
    
    # Your existing automation code here...
    # When you successfully generate a token:
    
    # OLD WAY (saving to tokens.json):
    # tokens.append({
    #     "id": token_id,
    #     "access_token": access_token,
    #     "tenant_url": tenant_url,
    #     ...
    # })
    # save_tokens_to_file(tokens)
    
    # NEW WAY (saving to API):
    result = api_client.save_token(
        access_token="your_generated_token",
        tenant_url="your_tenant_url",
        description="Token description",
        email_note="<EMAIL>",
        user_agent="your-automation-name"
    )
    
    if result.get('success'):
        print(f"Token saved to API: {result.get('tokenId')}")
    else:
        print(f"Failed to save token: {result.get('message')}")

if __name__ == "__main__":
    print("🧪 Testing Token API integration examples...\n")
    
    # Test DrissionPage integration
    print("1. Testing DrissionPage integration:")
    example_drissionpage_integration()
    
    print("\n2. Testing Real Browser integration:")
    example_real_browser_integration()
    
    print("\n✅ Integration examples completed!")
