#!/usr/bin/env node

/**
 * Complete test suite for automation script execution
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:9043';
const AUTH_TOKEN = 'your_secret_password_here_change_this';
const headers = { 'Authorization': `Bearer ${AUTH_TOKEN}`, 'Content-Type': 'application/json' };

async function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testCompleteFlow() {
  console.log('🧪 Testing complete automation flow...\n');

  try {
    // 1. Check initial state
    console.log('1. Checking initial token count...');
    let countResponse = await axios.get(`${BASE_URL}/api/tokens/valid-count`, { headers });
    console.log(`   Current valid tokens: ${countResponse.data.validCount}`);

    // 2. Test manual trigger first
    console.log('\n2. Testing manual automation trigger...');
    const triggerResponse = await axios.post(`${BASE_URL}/api/tokens/trigger-automation`,
      { count: 1 },
      { headers }
    );
    console.log(`   ✅ ${triggerResponse.data.message}`);
    console.log('   Waiting for script to complete...');
    await wait(5000); // Wait 5 seconds for script to complete

    // 3. Test automatic trigger by reducing token count
    console.log('\n3. Testing automatic trigger by simulating low token count...');
    console.log('   Marking 30 tokens as used to trigger automatic script...');

    const markResponse = await axios.post(`${BASE_URL}/api/tokens/test-mark-used`,
      { count: 30 },
      { headers }
    );
    console.log(`   ✅ ${markResponse.data.message}`);

    // 4. Check new token count
    countResponse = await axios.get(`${BASE_URL}/api/tokens/valid-count`, { headers });
    console.log(`   New valid token count: ${countResponse.data.validCount}`);

    if (countResponse.data.validCount < 6) {
      console.log('\n   🎯 Token count is now below minimum (6)');
      console.log('   The scheduled task should automatically trigger scripts within 1 hour');
      console.log('   Or you can wait for the next scheduled check to see automation trigger');
    }

    console.log('\n📝 Monitor the server console and logs/summary.log for:');
    console.log('   - Script execution logs');
    console.log('   - Sequential execution (3 second delay between scripts)');
    console.log('   - Success/failure detection');
    console.log('   - Automatic triggering when token count is low');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testCompleteFlow();
