#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9043';
const AUTH_TOKEN = 'your_secret_password_here_change_this';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};

async function testSequentialAutomation() {
  console.log('🧪 Testing sequential automation script execution...\n');

  try {
    // Test triggering 2 automation scripts
    console.log('Triggering 2 automation scripts sequentially...');
    const response = await axios.post(`${BASE_URL}/api/tokens/trigger-automation`, 
      { count: 2 }, 
      { headers }
    );
    
    console.log('✅ Automation triggered:', response.data.message);
    console.log('📝 Check the server logs and summary.log for execution details');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testSequentialAutomation();
