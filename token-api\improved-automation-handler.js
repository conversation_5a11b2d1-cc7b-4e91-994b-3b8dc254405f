#!/usr/bin/env node

/**
 * Improved Automation Script Handler
 * 
 * This module provides better handling for Python automation scripts:
 * 1. Sequential execution to avoid conflicts
 * 2. Process monitoring and logging
 * 3. Timeout handling
 * 4. Success/failure tracking
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class AutomationHandler {
  constructor(config, logFunction) {
    this.config = config;
    this.logSummary = logFunction;
    this.runningProcesses = new Set();
    this.processQueue = [];
    this.isProcessing = false;
  }

  /**
   * Trigger automation scripts with improved handling
   */
  async triggerAutomationScript(count = 1, reason = 'Scheduled check') {
    this.logSummary(`${reason}: Queuing ${count} automation script(s)`);
    
    // Add scripts to queue
    for (let i = 0; i < count; i++) {
      this.processQueue.push({
        id: `${Date.now()}_${i}`,
        reason: reason,
        timestamp: new Date().toISOString()
      });
    }
    
    // Start processing queue if not already processing
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  /**
   * Process automation scripts sequentially
   */
  async processQueue() {
    if (this.processQueue.length === 0) {
      this.isProcessing = false;
      return;
    }

    this.isProcessing = true;
    const task = this.processQueue.shift();
    
    try {
      await this.runSingleScript(task);
    } catch (error) {
      console.error(`[AUTOMATION] Script ${task.id} failed:`, error.message);
      this.logSummary(`Script ${task.id} failed: ${error.message}`);
    }

    // Wait a bit before next script to avoid conflicts
    setTimeout(() => {
      this.processQueue();
    }, 5000); // 5 second delay between scripts
  }

  /**
   * Run a single automation script with monitoring
   */
  runSingleScript(task) {
    return new Promise((resolve, reject) => {
      const scriptPath = path.join(__dirname, this.config.automationScript.workingDirectory);
      const timeout = 300000; // 5 minutes timeout
      
      console.log(`[AUTOMATION] Starting script ${task.id} (PID will be assigned)`);
      
      const child = spawn(this.config.automationScript.command, this.config.automationScript.args, {
        cwd: scriptPath,
        detached: false,  // Keep attached for monitoring
        stdio: ['ignore', 'pipe', 'pipe']  // Capture stdout and stderr
      });

      this.runningProcesses.add(child.pid);
      
      let stdout = '';
      let stderr = '';
      
      // Capture output
      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      // Set timeout
      const timeoutId = setTimeout(() => {
        console.log(`[AUTOMATION] Script ${task.id} (PID: ${child.pid}) timed out, killing...`);
        child.kill('SIGTERM');
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
      }, timeout);

      // Handle process completion
      child.on('close', (code, signal) => {
        clearTimeout(timeoutId);
        this.runningProcesses.delete(child.pid);
        
        const duration = Date.now() - new Date(task.timestamp).getTime();
        
        if (signal) {
          console.log(`[AUTOMATION] Script ${task.id} (PID: ${child.pid}) killed by signal ${signal}`);
          this.logSummary(`Script ${task.id} killed by signal ${signal} after ${Math.round(duration/1000)}s`);
          reject(new Error(`Process killed by signal ${signal}`));
        } else if (code === 0) {
          console.log(`[AUTOMATION] Script ${task.id} (PID: ${child.pid}) completed successfully`);
          this.logSummary(`Script ${task.id} completed successfully in ${Math.round(duration/1000)}s`);
          
          // Try to extract token count from output
          this.analyzeScriptOutput(stdout, stderr, task.id);
          resolve();
        } else {
          console.log(`[AUTOMATION] Script ${task.id} (PID: ${child.pid}) exited with code ${code}`);
          this.logSummary(`Script ${task.id} failed with exit code ${code} after ${Math.round(duration/1000)}s`);
          
          // Log error output for debugging
          if (stderr) {
            console.error(`[AUTOMATION] Script ${task.id} stderr:`, stderr.slice(-500)); // Last 500 chars
          }
          
          reject(new Error(`Process exited with code ${code}`));
        }
      });

      child.on('error', (error) => {
        clearTimeout(timeoutId);
        this.runningProcesses.delete(child.pid);
        console.error(`[AUTOMATION] Failed to start script ${task.id}:`, error.message);
        this.logSummary(`Failed to start script ${task.id}: ${error.message}`);
        reject(error);
      });

      console.log(`[AUTOMATION] Script ${task.id} started with PID: ${child.pid}`);
    });
  }

  /**
   * Analyze script output to extract useful information
   */
  analyzeScriptOutput(stdout, stderr, taskId) {
    // Look for success indicators in output
    const successPatterns = [
      /token.*saved.*successfully/i,
      /✅.*token/i,
      /successfully.*generated.*token/i,
      /token.*api.*success/i
    ];

    const errorPatterns = [
      /error/i,
      /failed/i,
      /exception/i,
      /❌/i
    ];

    let foundSuccess = false;
    let foundError = false;

    // Check stdout
    for (const pattern of successPatterns) {
      if (pattern.test(stdout)) {
        foundSuccess = true;
        break;
      }
    }

    for (const pattern of errorPatterns) {
      if (pattern.test(stdout) || pattern.test(stderr)) {
        foundError = true;
        break;
      }
    }

    if (foundSuccess) {
      this.logSummary(`Script ${taskId} output indicates token generation success`);
    } else if (foundError) {
      this.logSummary(`Script ${taskId} output indicates errors occurred`);
    } else {
      this.logSummary(`Script ${taskId} completed but output analysis inconclusive`);
    }

    // Log a sample of output for debugging (optional)
    if (stdout.length > 0) {
      console.log(`[AUTOMATION] Script ${taskId} stdout sample:`, stdout.slice(-200));
    }
  }

  /**
   * Get status of running processes
   */
  getStatus() {
    return {
      runningProcesses: this.runningProcesses.size,
      queuedProcesses: this.processQueue.length,
      isProcessing: this.isProcessing
    };
  }

  /**
   * Kill all running processes (emergency stop)
   */
  killAllProcesses() {
    console.log(`[AUTOMATION] Emergency stop: killing ${this.runningProcesses.size} running processes`);
    
    for (const pid of this.runningProcesses) {
      try {
        process.kill(pid, 'SIGTERM');
        setTimeout(() => {
          try {
            process.kill(pid, 'SIGKILL');
          } catch (e) {
            // Process already dead
          }
        }, 5000);
      } catch (error) {
        // Process already dead or not accessible
      }
    }
    
    this.runningProcesses.clear();
    this.processQueue = [];
    this.isProcessing = false;
    
    this.logSummary('Emergency stop: all automation processes killed');
  }
}

module.exports = AutomationHandler;
