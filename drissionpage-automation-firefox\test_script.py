#!/usr/bin/env python3
"""
Test script to simulate automation behavior
"""

import time
import sys
import random

def main():
    print("🚀 Starting test automation script...")
    
    # Simulate some work
    duration = random.randint(2, 8)  # 2-8 seconds
    print(f"⏳ Simulating automation work for {duration} seconds...")
    
    for i in range(duration):
        time.sleep(1)
        print(f"   Working... {i+1}/{duration}")
    
    # Simulate success/failure
    success = random.choice([True, True, True, False])  # 75% success rate
    
    if success:
        print("✅ Token generated and saved successfully")
        print("Token saved to API successfully")
        sys.exit(0)
    else:
        print("❌ Failed to generate token")
        print("Error occurred during automation")
        sys.exit(1)

if __name__ == "__main__":
    main()
