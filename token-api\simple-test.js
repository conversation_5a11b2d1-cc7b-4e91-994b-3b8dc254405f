#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:9043';
const AUTH_TOKEN = 'your_secret_password_here_change_this';

async function simpleTest() {
  try {
    console.log('Testing health endpoint...');
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('Health check response:', response.data);

    console.log('\nTesting valid token count...');
    const validResponse = await axios.get(`${BASE_URL}/api/tokens/valid-count`, {
      headers: { 'Authorization': `Bearer ${AUTH_TOKEN}` }
    });
    console.log('Valid count response:', validResponse.data);

  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

simpleTest();
